#!/usr/bin/env python3
"""
测试需求编号生成和Git分支创建的修复
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.requirement import RequirementType
from app.api.requirement import generate_requirement_code

def test_requirement_code_generation():
    """测试需求编号生成逻辑"""
    print("=== 测试需求编号生成逻辑 ===")
    
    # 创建内存数据库进行测试
    engine = create_engine("sqlite:///:memory:")
    
    # 创建表结构
    from app.core.database import Base
    Base.metadata.create_all(bind=engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 测试项目1的需求编号生成
        print("\n--- 项目1的需求编号生成 ---")
        project_id_1 = 1
        
        # 第一个Bug修复需求
        code1 = generate_requirement_code(db, project_id_1, RequirementType.BUG_FIX)
        print(f"项目1第一个Bug修复需求编号: {code1}")
        assert code1 == "B0001", f"期望 B0001，实际 {code1}"
        
        # 第一个新功能需求
        code2 = generate_requirement_code(db, project_id_1, RequirementType.NEW_FEATURE)
        print(f"项目1第一个新功能需求编号: {code2}")
        assert code2 == "F0001", f"期望 F0001，实际 {code2}"
        
        # 第一个热修复需求
        code3 = generate_requirement_code(db, project_id_1, RequirementType.HOT_FIX_BUG)
        print(f"项目1第一个热修复需求编号: {code3}")
        assert code3 == "H0001", f"期望 H0001，实际 {code3}"
        
        # 测试项目2的需求编号生成（应该从1开始）
        print("\n--- 项目2的需求编号生成 ---")
        project_id_2 = 2
        
        # 项目2的第一个Bug修复需求
        code4 = generate_requirement_code(db, project_id_2, RequirementType.BUG_FIX)
        print(f"项目2第一个Bug修复需求编号: {code4}")
        assert code4 == "B0001", f"期望 B0001，实际 {code4}"
        
        print("\n✅ 需求编号生成测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise
    finally:
        db.close()

def test_branch_name_format():
    """测试分支名称格式"""
    print("\n=== 测试分支名称格式 ===")
    
    # 模拟分支名称生成逻辑
    requirement_code = "B0237"
    branch_name = "test_release_bug"
    
    # Bug修复分支
    bugfix_branch = f"bugfix/{requirement_code}-{branch_name}"
    print(f"Bug修复分支名称: {bugfix_branch}")
    assert bugfix_branch == "bugfix/B0237-test_release_bug", f"期望 bugfix/B0237-test_release_bug，实际 {bugfix_branch}"
    
    # 新功能分支
    feature_code = "F0001"
    feature_branch_name = "user_management"
    feature_branch = f"feature/{feature_code}-{feature_branch_name}"
    print(f"新功能分支名称: {feature_branch}")
    assert feature_branch == "feature/F0001-user_management", f"期望 feature/F0001-user_management，实际 {feature_branch}"
    
    # 热修复分支
    hotfix_code = "H0001"
    hotfix_branch_name = "critical_security_fix"
    hotfix_branch = f"hotfix/{hotfix_code}-{hotfix_branch_name}"
    print(f"热修复分支名称: {hotfix_branch}")
    assert hotfix_branch == "hotfix/H0001-critical_security_fix", f"期望 hotfix/H0001-critical_security_fix，实际 {hotfix_branch}"
    
    print("\n✅ 分支名称格式测试通过！")

if __name__ == "__main__":
    print("开始测试需求编号和分支名称修复...")
    
    try:
        test_requirement_code_generation()
        test_branch_name_format()
        print("\n🎉 所有测试通过！修复成功！")
    except Exception as e:
        print(f"\n💥 测试失败: {e}")
        sys.exit(1)
