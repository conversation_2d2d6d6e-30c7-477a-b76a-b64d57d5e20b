<template>
  <div class="requirement-card" @click="handleCardClick">
    <!-- 卡片头部 -->
    <div class="card-header" :class="getCardHeaderClass(requirement.type)">
      <div class="requirement-id">{{ requirement.requirementCode }}</div>
      <div class="project-name">{{ requirement.projectName }}</div>
    </div>

    <!-- 卡片主体 -->
    <div class="card-body" @click.stop>
      <div class="requirement-title">{{ requirement.requirementName }}</div>

      <!-- 卡片元信息 -->
      <div class="card-meta">
        <span class="priority-tag" :class="getPriorityClass(requirement.priority)">{{ requirement.priority }}</span>
        <span class="badge" :class="getTypeClass(requirement.type)">{{ requirement.typeDisplay }}</span>
        <span class="badge" :class="getStatusClass(requirement.status)">{{ getStatusDisplayName(requirement.status) }}</span>
      </div>

      <!-- 时间信息 -->
      <div class="card-time">
        <div class="time-item">
          <el-icon class="time-icon"><Clock /></el-icon>
          开始：{{ formatToDate(requirement.startDate) }}
        </div>
        <div class="time-item">
          <el-icon class="time-icon"><Clock /></el-icon>
          截止：{{ formatToDate(requirement.endDate) }}
        </div>
      </div>

      <!-- 负责人信息 -->
      <div class="card-assignee">
        <div class="assignee-info">
          <template v-if="!requirement.developers || requirement.developers.length === 0">
            <span class="assignee-label">负责人：</span>
            <span class="assignee-value unassigned">未分配</span>
          </template>
          <template v-else>
            <span class="assignee-label">开发者：</span>
            <span class="assignee-value">{{ Array.isArray(requirement.developers) ? requirement.developers.map(d => d.name).join(', ') : requirement.developers }}</span>
          </template>
        </div>
      </div>

      <!-- 角色信息 -->
      <div class="card-roles">
        <span v-if="isRequirementDeveloper" class="role-tag role-developer">开发人员</span>
        <span v-if="isRequirementTester" class="role-tag role-tester">测试人员</span>
      </div>

      <!-- 操作按钮 -->
      <div class="card-actions">
        <!-- 根据权限矩阵显示操作按钮 -->
        <template v-if="canViewRequirement">
          <button class="action-btn action-view" @click="$emit('view', requirement)">查看</button>
        </template>

        <!-- 草稿状态：只有管理员可以操作 -->
        <template v-if="requirement.status === '草稿' && userRole === 'admin'">
          <button class="action-btn action-edit" @click="$emit('edit', requirement)">编辑</button>
          <button class="action-btn action-publish" @click="$emit('publish', requirement)">发布</button>
          <button class="action-btn action-delete" @click="$emit('delete', requirement)">删除</button>
        </template>

        <!-- 管理员可以在任何阶段删除需求 -->
        <template v-if="userRole === 'admin' && requirement.status !== '草稿'">
          <button class="action-btn action-delete" @click="$emit('delete', requirement)">删除</button>
        </template>

        <!-- 待认领状态：开发人员可以认领 -->
        <template v-if="requirement.status === '待处理' && userRole === 'developer' && isRequirementDeveloper">
          <button class="action-btn action-claim" @click="$emit('claim', requirement)">认领任务</button>
        </template>

        <!-- 开发中状态：开发人员可以提交测试 -->
        <template v-if="requirement.status === '开发中' && userRole === 'developer' && isRequirementDeveloper">
          <button class="action-btn action-submit" @click="$emit('submitToTest', requirement)">提交测试</button>
        </template>

        <!-- 测试中状态：开发人员可以撤回测试，测试人员可以测试操作 -->
        <template v-if="requirement.status === '测试中'">
          <template v-if="userRole === 'developer' && isRequirementDeveloper">
            <button class="action-btn action-withdraw" @click="$emit('withdrawFromTest', requirement)">撤回测试</button>
          </template>
          <template v-if="userRole === 'tester' && isRequirementTester">
            <button class="action-btn action-approve" @click="$emit('approveTest', requirement)">通过测试</button>
            <button class="action-btn action-reject" @click="$emit('rejectTest', requirement)">驳回测试</button>
          </template>
        </template>

        <!-- 验证中状态：管理员可以验证操作，开发人员和测试人员可以撤回验证 -->
        <template v-if="requirement.status === '验证中'">
          <template v-if="userRole === 'admin'">
            <button class="action-btn action-approve" @click="$emit('approveValidation', requirement)">通过验证</button>
            <button class="action-btn action-reject" @click="$emit('rejectValidation', requirement)">驳回验证</button>
          </template>
          <template v-if="(userRole === 'developer' && isRequirementDeveloper) || (userRole === 'tester' && isRequirementTester)">
            <button class="action-btn action-withdraw" @click="$emit('withdrawFromValidation', requirement)">撤回验证</button>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { Clock } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  requirement: {
    type: Object,
    required: true
  },
  userRole: {
    type: String,
    required: true,
    validator: (value) => ['admin', 'developer', 'tester', 'project_manager'].includes(value)
  },
  currentUserId: {
    type: Number,
    required: true
  }
});

// Emits
const emit = defineEmits([
  'view', 'edit', 'publish', 'delete', 'claim', 'submitToTest',
  'withdrawFromTest', 'withdrawFromValidation', 'approveTest', 'rejectTest',
  'approveValidation', 'rejectValidation'
]);

// 状态映射（后端返回中文状态值）
const statusMap = {
  '草稿': '草稿',
  '待处理': '待认领',
  '开发中': '开发中',
  '测试中': '测试中',
  '验证中': '验证中',
  '已完成': '已完成'
};

// 检查当前用户是否是需求的开发人员
const isRequirementDeveloper = computed(() => {
  if (!props.requirement.developers) return false;
  if (Array.isArray(props.requirement.developers)) {
    return props.requirement.developers.some(dev =>
      (typeof dev === 'object' ? dev.id : dev) == props.currentUserId
    );
  }
  return props.requirement.developers == props.currentUserId;
});

// 检查当前用户是否是需求的测试人员
const isRequirementTester = computed(() => {
  if (!props.requirement.testers) return false;
  if (Array.isArray(props.requirement.testers)) {
    return props.requirement.testers.some(tester =>
      (typeof tester === 'object' ? tester.id : tester) == props.currentUserId
    );
  }
  return props.requirement.testers == props.currentUserId;
});

// 检查是否可以查看需求
const canViewRequirement = computed(() => {
  const status = props.requirement.status;
  const role = props.userRole;

  // 草稿状态只有管理员可以看到
  if (status === '草稿') {
    return role === 'admin';
  }

  // 其他状态：管理员、项目管理部可以查看所有，开发人员和测试人员只能查看相关的需求
  if (role === 'admin' || role === 'project_manager') {
    return true;
  }

  if (role === 'developer') {
    return isRequirementDeveloper.value;
  }

  if (role === 'tester') {
    return isRequirementTester.value;
  }

  return false;
});

// 处理卡片点击事件
const handleCardClick = () => {
  if (canViewRequirement.value) {
    emit('view', props.requirement);
  }
};

// 格式化日期
const formatToDate = (dateString) => {
  if (!dateString) return '未设置';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;

  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).replace(/\//g, '-');
};

// 获取状态显示名称
const getStatusDisplayName = (status) => {
  return statusMap[status] || status;
};

// 获取卡片头部样式类
const getCardHeaderClass = (type) => {
  switch (type) {
    case 'Bug Fixed': return 'type-bug';
    case 'Hot Bug Fixed': return 'type-hotbug';
    case 'New Feature': return 'type-feature';
    default: return 'type-feature';
  }
};

// 获取优先级样式类
const getPriorityClass = (priority) => {
  switch (priority) {
    case 'P0': return 'priority-p0';
    case 'P1': return 'priority-p1';
    case 'P2': return 'priority-p2';
    default: return 'priority-p2';
  }
};

// 获取类型样式类
const getTypeClass = (type) => {
  switch (type) {
    case 'Bug Fixed': return 'badge-orange';
    case 'Hot Bug Fixed': return 'badge-red';
    case 'New Feature': return 'badge-blue';
    default: return 'badge-blue';
  }
};

// 获取状态对应的CSS类
const getStatusClass = (status) => {
  switch (status) {
    case '草稿':
      return 'badge-gray';
    case '待处理':
      return 'badge-purple';
    case '开发中':
      return 'badge-blue';
    case '测试中':
      return 'badge-orange';
    case '验证中':
      return 'badge-green';
    case '已完成':
      return 'badge-success';
    default:
      return 'badge-gray';
  }
};
</script>

<style scoped>
/* 需求卡片样式 */
.requirement-card {
  background-color: #fff;
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
  border: 1px solid var(--border-color);
}

.requirement-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

/* 卡片头部 */
.card-header {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  position: relative;
}

.card-header.type-bug {
  background: linear-gradient(135deg, #f5a623, #ffd666);
}

.card-header.type-hotbug {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.card-header.type-feature {
  background: linear-gradient(135deg, #1877f2, #36cfc9);
}

.requirement-id {
  font-size: 20px;
  font-weight: bold;
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.project-name {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 卡片主体 */
.card-body {
  padding: 20px;
}

.requirement-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 12px;
  line-height: 1.4;
  height: 44px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 卡片元信息 */
.card-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.card-meta .badge {
  font-size: 12px;
  height: 22px;
  padding: 0 8px;
}

/* 优先级标签 */
.priority-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 22px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.priority-p0 {
  background-color: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.priority-p1 {
  background-color: rgba(245, 166, 35, 0.1);
  color: #f5a623;
}

.priority-p2 {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

/* 标签样式 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  padding: 0 10px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
}

.badge-blue {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.badge-green {
  background-color: var(--success-light);
  color: var(--success);
}

.badge-orange {
  background-color: var(--warning-light);
  color: var(--warning);
}

.badge-red {
  background-color: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.badge-gray {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.badge-purple {
  background-color: var(--purple-light);
  color: var(--purple);
}

/* 时间信息 */
.card-time {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 13px;
  color: var(--text-hint);
}

.time-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.time-icon {
  width: 14px;
  height: 14px;
  opacity: 0.6;
}

/* 负责人信息 */
.card-assignee {
  margin-bottom: 12px;
}

.assignee-info {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: var(--text-secondary);
}

.assignee-label {
  font-weight: 500;
  margin-right: 4px;
}

.assignee-value {
  color: var(--text-primary);
}

.assignee-value.unassigned {
  color: var(--text-hint);
  font-style: italic;
}

/* 角色信息 */
.card-roles {
  display: flex;
  gap: 4px;
  margin-bottom: 16px;
}

.role-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.role-developer {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.role-tester {
  background-color: var(--success-light);
  color: var(--success);
}

/* 卡片底部操作区 */
.card-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 操作按钮样式 */
:deep(.action-btn) {
  padding: 6px 12px;
  font-size: 13px;
  border-radius: 4px;
  border: 1px solid;
  cursor: pointer;
  transition: all 0.3s;
  background-color: transparent;
}

:deep(.action-view) {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.action-claim) {
  color: var(--success);
  border-color: var(--success);
}

:deep(.action-edit) {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.action-publish) {
  color: var(--success);
  border-color: var(--success);
}

:deep(.action-delete) {
  color: var(--error);
  border-color: var(--error);
}

:deep(.action-submit) {
  color: var(--warning);
  border-color: var(--warning);
}

:deep(.action-withdraw) {
  color: var(--text-hint);
  border-color: var(--border-color);
}

:deep(.action-approve) {
  color: var(--success);
  border-color: var(--success);
}

:deep(.action-reject) {
  color: var(--error);
  border-color: var(--error);
}

:deep(.action-btn:hover) {
  opacity: 0.8;
  transform: scale(1.02);
}
</style>
